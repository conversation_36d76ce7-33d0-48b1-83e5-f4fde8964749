const twilio = require('twilio');
const { query } = require('../config/database');

// Initialize Twilio client with error handling
let client = null;
try {
    if (process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) {
        client = twilio(
            process.env.TWILIO_ACCOUNT_SID,
            process.env.TWILIO_AUTH_TOKEN
        );
    } else {
        console.warn('⚠️  Twilio credentials not configured. WhatsApp features will be disabled.');
    }
} catch (error) {
    console.error('Failed to initialize Twilio client:', error.message);
}

// Format phone number for WhatsApp
const formatPhoneNumber = (phoneNumber) => {
    // Remove any non-digit characters
    let cleaned = phoneNumber.replace(/\D/g, '');
    
    // Add country code if not present
    if (cleaned.length === 10) {
        cleaned = '91' + cleaned; // Add India country code
    }
    
    return `whatsapp:+${cleaned}`;
};

// Send WhatsApp message
const sendWhatsAppMessage = async (phoneNumber, message, leadId = null) => {
    try {
        // Check if Twilio client is available
        if (!client) {
            console.warn('Twilio client not available. WhatsApp message not sent.');

            // Log the failed message in database
            if (leadId) {
                await logWhatsAppMessage(
                    leadId,
                    phoneNumber,
                    message,
                    null,
                    'failed',
                    'Twilio client not configured'
                );
            }

            return {
                success: false,
                error: 'WhatsApp service not configured'
            };
        }

        const formattedNumber = formatPhoneNumber(phoneNumber);
        const fromNumber = process.env.TWILIO_WHATSAPP_NUMBER || 'whatsapp:+14155238886';

        console.log(`Sending WhatsApp message to ${formattedNumber}`);

        const messageResponse = await client.messages.create({
            body: message,
            from: fromNumber,
            to: formattedNumber
        });

        console.log(`WhatsApp message sent successfully. SID: ${messageResponse.sid}`);

        // Log the message in database
        if (leadId) {
            await logWhatsAppMessage(
                leadId,
                phoneNumber,
                message,
                messageResponse.sid,
                'sent'
            );

            // Update lead to mark WhatsApp as sent
            await query(
                'UPDATE booking_leads SET whatsapp_sent = true, whatsapp_sent_at = CURRENT_TIMESTAMP WHERE id = $1',
                [leadId]
            );
        }

        return {
            success: true,
            messageSid: messageResponse.sid,
            status: messageResponse.status
        };

    } catch (error) {
        console.error('WhatsApp message error:', error);

        // Log the failed message in database
        if (leadId) {
            await logWhatsAppMessage(
                leadId,
                phoneNumber,
                message,
                null,
                'failed',
                error.message
            );
        }

        throw error;
    }
};

// Log WhatsApp message in database
const logWhatsAppMessage = async (leadId, phoneNumber, messageContent, messageSid, status, errorMessage = null) => {
    try {
        const logQuery = `
            INSERT INTO whatsapp_messages (
                lead_id, phone_number, message_content, message_sid, status, error_message
            ) VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING id
        `;
        
        const values = [
            leadId,
            phoneNumber,
            messageContent,
            messageSid,
            status,
            errorMessage
        ];
        
        await query(logQuery, values);
        console.log('WhatsApp message logged in database');
        
    } catch (error) {
        console.error('Failed to log WhatsApp message:', error);
    }
};

// Send booking confirmation message
const sendBookingConfirmation = async (leadData) => {
    const message = `
🏠 *Packers & Movers Booking Confirmation*

Dear ${leadData.customer_name},

Thank you for choosing our services! We have received your booking request.

📋 *Booking Details:*
• From: ${leadData.from_city}, ${leadData.from_state}
• To: ${leadData.to_city}, ${leadData.to_state}
• Date: ${new Date(leadData.tentative_date).toLocaleDateString('en-IN')}
• Rooms: ${leadData.number_of_rooms}
• Service: ${leadData.service_type.charAt(0).toUpperCase() + leadData.service_type.slice(1)}

📞 Our team will contact you within 2 hours to discuss your requirements and provide a detailed quote.

🆔 Booking Reference: ${leadData.id.substring(0, 8).toUpperCase()}

For any queries, call us at: +91-XXXXXXXXXX

Best regards,
Packers & Movers Team
    `.trim();
    
    return await sendWhatsAppMessage(leadData.phone_number, message, leadData.id);
};

// Send quote message
const sendQuoteMessage = async (leadData, quoteAmount) => {
    const message = `
💰 *Quote for Your Moving Request*

Dear ${leadData.customer_name},

We have prepared a quote for your moving requirements:

📋 *Service Details:*
• Route: ${leadData.from_city} → ${leadData.to_city}
• Date: ${new Date(leadData.tentative_date).toLocaleDateString('en-IN')}
• Rooms: ${leadData.number_of_rooms}

💵 *Estimated Cost: ₹${quoteAmount.toLocaleString('en-IN')}*

This quote includes:
✅ Packing materials
✅ Loading & unloading
✅ Transportation
✅ Basic insurance

📞 To confirm your booking or discuss details, please call us at: +91-XXXXXXXXXX

🆔 Reference: ${leadData.id.substring(0, 8).toUpperCase()}

Best regards,
Packers & Movers Team
    `.trim();
    
    return await sendWhatsAppMessage(leadData.phone_number, message, leadData.id);
};

// Send status update message
const sendStatusUpdateMessage = async (leadData, newStatus, notes = '') => {
    let statusMessage = '';
    
    switch (newStatus) {
        case 'contacted':
            statusMessage = '📞 We have contacted you regarding your moving request.';
            break;
        case 'confirmed':
            statusMessage = '✅ Your booking has been confirmed! Our team will reach out with further details.';
            break;
        case 'in_progress':
            statusMessage = '🚛 Your moving service is now in progress. Our team is on the way!';
            break;
        case 'completed':
            statusMessage = '🎉 Your moving service has been completed successfully! Thank you for choosing us.';
            break;
        case 'cancelled':
            statusMessage = '❌ Your booking has been cancelled. If you have any questions, please contact us.';
            break;
        default:
            statusMessage = `📋 Your booking status has been updated to: ${newStatus}`;
    }
    
    const message = `
🏠 *Booking Status Update*

Dear ${leadData.customer_name},

${statusMessage}

🆔 Reference: ${leadData.id.substring(0, 8).toUpperCase()}

${notes ? `📝 Note: ${notes}` : ''}

For any queries, call us at: +91-XXXXXXXXXX

Best regards,
Packers & Movers Team
    `.trim();
    
    return await sendWhatsAppMessage(leadData.phone_number, message, leadData.id);
};

// Check message delivery status
const checkMessageStatus = async (messageSid) => {
    try {
        if (!client) {
            throw new Error('Twilio client not configured');
        }

        const message = await client.messages(messageSid).fetch();

        // Update status in database
        await query(
            'UPDATE whatsapp_messages SET status = $1, delivered_at = CURRENT_TIMESTAMP WHERE message_sid = $2',
            [message.status, messageSid]
        );

        return message.status;

    } catch (error) {
        console.error('Failed to check message status:', error);
        throw error;
    }
};

module.exports = {
    sendWhatsAppMessage,
    sendBookingConfirmation,
    sendQuoteMessage,
    sendStatusUpdateMessage,
    checkMessageStatus,
    formatPhoneNumber
};
