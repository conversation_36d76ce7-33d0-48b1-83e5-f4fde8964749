# Packers & Movers Website Setup Guide

## Prerequisites

1. **Node.js** (v16 or higher)
2. **PostgreSQL** (v12 or higher)
3. **Twilio Account** (for WhatsApp integration)

## Installation Steps

### 1. Database Setup

1. Install PostgreSQL and create a database:
```sql
CREATE DATABASE packers_movers_db;
```

2. Run the schema and seed data:
```bash
psql -U postgres -d packers_movers_db -f database/schema.sql
psql -U postgres -d packers_movers_db -f database/seed_data.sql
```

### 2. Environment Configuration

1. Copy `.env.example` to `.env`:
```bash
cp .env.example .env
```

2. Update the `.env` file with your actual values:
   - Database credentials
   - JWT secret (generate a strong random string)
   - Twilio credentials for WhatsApp
   - Admin credentials

### 3. Twilio WhatsApp Setup

1. Create a Twilio account at https://www.twilio.com
2. Get your Account SID and Auth Token
3. Set up WhatsApp Sandbox or get approved for production
4. Update the `.env` file with your Twilio credentials

### 4. Install Dependencies

```bash
npm install
```

### 5. Start the Application

For development:
```bash
npm run dev
```

For production:
```bash
npm start
```

## Default Admin Credentials

- **Email**: <EMAIL>
- **Password**: admin123

**⚠️ Important**: Change these credentials immediately after first login!

## Application URLs

- **Frontend**: http://localhost:3000
- **Admin Panel**: http://localhost:3000/admin
- **Health Check**: http://localhost:3000/health

## Features

### Customer Features
- Modern, responsive website design
- Booking form with Indian cities dropdown
- Form validation and error handling
- WhatsApp confirmation messages
- Booking status tracking

### Admin Features
- Secure login system
- Dashboard with statistics
- Lead management and filtering
- Status updates with notifications
- Real-time data updates

## Database Schema

The application uses the following main tables:
- `admin_users` - Admin user accounts
- `booking_leads` - Customer booking requests
- `indian_cities` - List of Indian cities and states
- `lead_status_history` - Status change tracking
- `whatsapp_messages` - Message delivery logs

## API Endpoints

### Public APIs
- `POST /api/bookings/submit` - Submit booking request
- `GET /api/bookings/status/:referenceNumber` - Get booking status
- `GET /api/cities` - Get Indian cities
- `GET /api/states` - Get Indian states

### Admin APIs
- `POST /api/admin/login` - Admin login
- `GET /api/admin/profile` - Get admin profile
- `GET /api/admin/leads` - Get all leads
- `PUT /api/admin/leads/:id/status` - Update lead status
- `GET /api/admin/dashboard/stats` - Get dashboard statistics

## Security Features

- JWT-based authentication
- Password hashing with bcrypt
- Rate limiting on API endpoints
- Input validation and sanitization
- CORS protection
- Helmet security headers

## Deployment

### Environment Variables for Production

Make sure to set these environment variables in production:
- `NODE_ENV=production`
- Strong `JWT_SECRET`
- Production database credentials
- Valid Twilio credentials

### Database Migration

For production deployment, ensure the database schema is properly set up:
1. Create the production database
2. Run the schema.sql file
3. Run the seed_data.sql file
4. Update the default admin password

### SSL/HTTPS

For production, ensure you have SSL certificates configured and update the CORS settings in `server.js`.

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check PostgreSQL is running
   - Verify database credentials in `.env`
   - Ensure database exists

2. **WhatsApp Messages Not Sending**
   - Verify Twilio credentials
   - Check WhatsApp sandbox setup
   - Ensure phone numbers are in correct format

3. **Admin Login Issues**
   - Check if admin user exists in database
   - Verify password hash in database
   - Check JWT secret configuration

### Logs

Application logs are output to the console. In production, consider using a logging service like Winston or similar.

## Support

For technical support or questions about the application, please refer to the code documentation or contact the development team.
