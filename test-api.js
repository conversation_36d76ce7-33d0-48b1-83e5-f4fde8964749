// Simple API test script
const http = require('http');

function makeRequest(path, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: path,
            method: method,
            headers: {
                'Content-Type': 'application/json',
            }
        };

        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonBody = JSON.parse(body);
                    resolve({ status: res.statusCode, data: jsonBody });
                } catch (e) {
                    resolve({ status: res.statusCode, data: body });
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }

        req.end();
    });
}

async function testAPI() {
    console.log('🧪 Testing Packers & Movers API...\n');

    try {
        // Test health endpoint
        console.log('1. Testing health endpoint...');
        const health = await makeRequest('/health');
        console.log(`   Status: ${health.status}`);
        console.log(`   Response:`, health.data);
        console.log('');

        // Test states endpoint
        console.log('2. Testing states endpoint...');
        const states = await makeRequest('/api/states');
        console.log(`   Status: ${states.status}`);
        console.log(`   States count: ${states.data.success ? states.data.data.length : 'Error'}`);
        console.log('');

        // Test cities endpoint
        console.log('3. Testing cities endpoint...');
        const cities = await makeRequest('/api/cities?limit=5');
        console.log(`   Status: ${cities.status}`);
        console.log(`   Cities count: ${cities.data.success ? cities.data.data.length : 'Error'}`);
        console.log('');

        // Test service types endpoint
        console.log('4. Testing service types endpoint...');
        const serviceTypes = await makeRequest('/api/service-types');
        console.log(`   Status: ${serviceTypes.status}`);
        console.log(`   Service types count: ${serviceTypes.data.success ? serviceTypes.data.data.length : 'Error'}`);
        console.log('');

        // Test booking submission (will fail due to validation, but should return proper error)
        console.log('5. Testing booking submission (invalid data)...');
        const booking = await makeRequest('/api/bookings/submit', 'POST', {
            customerName: 'Test User'
            // Missing required fields
        });
        console.log(`   Status: ${booking.status}`);
        console.log(`   Response: ${booking.data.error || 'Success'}`);
        console.log('');

        console.log('✅ API tests completed!');
        console.log('\n📋 Summary:');
        console.log('- Server is running and responding');
        console.log('- Basic endpoints are working');
        console.log('- Error handling is functioning');
        console.log('\n⚠️  Note: Database-dependent features will not work without PostgreSQL setup');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testAPI();
